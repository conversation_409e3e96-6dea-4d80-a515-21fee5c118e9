{"name": "bladerf-video", "version": "1.0.0", "description": "Node.js C++ addon for BladeRF video processing", "main": "index.js", "scripts": {"install": "node-gyp rebuild", "configure-xcode": "node-gyp configure -- -f xcode", "open-xcode": "open ./build/binding.xcodeproj", "configure": "node-gyp configure", "build": "node-gyp build", "clean": "node-gyp clean", "test": "node test.js", "test-iq": "node test-iq-recording.js", "example": "node example.js", "install-rpi": "./install-rpi.sh", "cross-compile": "./cross-compile-arm.sh", "cross-compile-macos": "./cross-compile-macos.sh", "cross-compile-docker": "./docker-cross-compile.sh"}, "keywords": ["blade<PERSON>", "sdr", "video", "native", "addon"], "author": "", "license": "MIT", "devDependencies": {"node-gyp": "^10.0.0"}, "engines": {"node": ">=14.0.0"}, "gypfile": true}